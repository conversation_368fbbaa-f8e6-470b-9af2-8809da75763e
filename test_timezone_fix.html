<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Timezone Fix Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.43/moment-timezone-with-data.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <h1>StreamOnPod Timezone Fix Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Timezone Conversion Logic</h2>
        <p>Testing the fixed timezone conversion logic with different timezones</p>
        <div id="test1-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Date Format Based on Locale</h2>
        <p>Testing date formatting for different locales (EN/ID)</p>
        <div id="test2-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Interactive Timezone Test</h2>
        <label for="test-datetime">Select Date/Time:</label>
        <input type="datetime-local" id="test-datetime" value="2025-07-12T20:00">
        <br><br>
        <label for="test-timezone">Select Timezone:</label>
        <select id="test-timezone">
            <option value="Asia/Jakarta">Asia/Jakarta (WIB)</option>
            <option value="America/New_York">America/New_York (EST/EDT)</option>
            <option value="Europe/London">Europe/London (GMT/BST)</option>
            <option value="Asia/Tokyo">Asia/Tokyo (JST)</option>
            <option value="Australia/Sydney">Australia/Sydney (AEST/AEDT)</option>
        </select>
        <br><br>
        <button onclick="testTimezoneConversion()">Test Conversion</button>
        <div id="test3-result" class="result"></div>
    </div>

    <script>
        // Simulate the fixed updateHelperText function
        function updateHelperText(scheduleTime, selectedTimezone, locale = 'id') {
            if (!scheduleTime) {
                return locale === 'en' ? 
                    'Please fill in the schedule time to see the conversion preview.' :
                    'Silakan isi waktu jadwal untuk melihat pratinjau konversi.';
            }
            
            try {
                // Parse the datetime-local input in the selected timezone (FIXED LOGIC)
                const userZonedTime = moment.tz(scheduleTime, "YYYY-MM-DDTHH:mm", selectedTimezone);
                
                // Convert to server timezone (Asia/Jakarta) for reference
                const serverTime = userZonedTime.clone().tz('Asia/Jakarta');
                
                // Convert to UTC for reference
                const utcTime = userZonedTime.clone().utc();
                
                // Format displays based on current locale
                const timeFormat = locale === 'en' ? 'dddd, MMMM DD, YYYY [at] HH:mm' : 'dddd, DD MMMM YYYY [pukul] HH:mm';
                
                const userDisplay = userZonedTime.format(timeFormat);
                const serverDisplay = serverTime.format(timeFormat);
                const utcDisplay = utcTime.format(timeFormat) + ' UTC';
                
                // Get translated text
                const selectedTimeText = locale === 'en' ? 'Your selected time' : 'Waktu yang Anda pilih';
                const serverTimeText = locale === 'en' ? 'Server Time (WIB)' : 'Waktu Server (WIB)';
                const utcTimeText = locale === 'en' ? 'UTC Time' : 'Waktu UTC';
                const serverNoteText = locale === 'en' ? 
                    '💡 Server will use WIB time for scheduling' : 
                    '💡 Server akan menggunakan waktu WIB untuk penjadwalan';
                
                let helperHTML = `
                    <div>
                        <div><strong>${selectedTimeText} (${selectedTimezone}):</strong> ${userDisplay}</div>
                `;
                
                // If timezone is different from server, show conversion
                if (selectedTimezone !== 'Asia/Jakarta') {
                    helperHTML += `<div><strong>${serverTimeText}:</strong> ${serverDisplay}</div>`;
                }
                
                helperHTML += `
                        <div><strong>${utcTimeText}:</strong> ${utcDisplay}</div>
                        <div style="color: #1976d2; font-size: 0.9em; margin-top: 5px;">${serverNoteText}</div>
                    </div>
                `;
                
                return helperHTML;
            } catch (error) {
                return locale === 'en' ? 'Invalid time format' : 'Format waktu tidak valid';
            }
        }

        // Test 1: Basic timezone conversion logic
        function runTest1() {
            const testCases = [
                { time: '2025-07-12T20:00', timezone: 'America/New_York', locale: 'en' },
                { time: '2025-07-12T20:00', timezone: 'Asia/Tokyo', locale: 'id' },
                { time: '2025-07-12T20:00', timezone: 'Asia/Jakarta', locale: 'en' },
            ];

            let results = '<h3>Test Results:</h3>';
            let allPassed = true;

            testCases.forEach((testCase, index) => {
                try {
                    const result = updateHelperText(testCase.time, testCase.timezone, testCase.locale);
                    const hasCorrectTimezone = result.includes(testCase.timezone);
                    const hasServerTime = testCase.timezone !== 'Asia/Jakarta' ? result.includes('Server Time') || result.includes('Waktu Server') : true;
                    const hasUTC = result.includes('UTC');
                    
                    const passed = hasCorrectTimezone && hasServerTime && hasUTC;
                    allPassed = allPassed && passed;
                    
                    results += `
                        <div style="margin: 10px 0; padding: 10px; background: ${passed ? '#e8f5e8' : '#ffebee'};">
                            <strong>Test Case ${index + 1}:</strong> ${testCase.timezone} (${testCase.locale})<br>
                            <strong>Status:</strong> ${passed ? '✅ PASSED' : '❌ FAILED'}<br>
                            <div style="margin-top: 5px; font-size: 0.9em;">${result}</div>
                        </div>
                    `;
                } catch (error) {
                    allPassed = false;
                    results += `
                        <div style="margin: 10px 0; padding: 10px; background: #ffebee;">
                            <strong>Test Case ${index + 1}:</strong> ${testCase.timezone} (${testCase.locale})<br>
                            <strong>Status:</strong> ❌ ERROR<br>
                            <strong>Error:</strong> ${error.message}
                        </div>
                    `;
                }
            });

            results += `<div style="margin-top: 15px; padding: 10px; background: ${allPassed ? '#e8f5e8' : '#ffebee'}; font-weight: bold;">
                Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}
            </div>`;

            document.getElementById('test1-result').innerHTML = results;
        }

        // Test 2: Locale-based formatting
        function runTest2() {
            const testTime = '2025-07-12T20:00';
            const testTimezone = 'America/New_York';
            
            const enResult = updateHelperText(testTime, testTimezone, 'en');
            const idResult = updateHelperText(testTime, testTimezone, 'id');
            
            const enHasCorrectFormat = enResult.includes('at') && enResult.includes('Your selected time');
            const idHasCorrectFormat = idResult.includes('pukul') && idResult.includes('Waktu yang Anda pilih');
            
            const passed = enHasCorrectFormat && idHasCorrectFormat;
            
            const results = `
                <div style="margin: 10px 0; padding: 10px; background: ${passed ? '#e8f5e8' : '#ffebee'};">
                    <strong>English Format Test:</strong> ${enHasCorrectFormat ? '✅ PASSED' : '❌ FAILED'}<br>
                    <div style="margin: 5px 0; font-size: 0.9em;">${enResult}</div>
                </div>
                <div style="margin: 10px 0; padding: 10px; background: ${passed ? '#e8f5e8' : '#ffebee'};">
                    <strong>Indonesian Format Test:</strong> ${idHasCorrectFormat ? '✅ PASSED' : '❌ FAILED'}<br>
                    <div style="margin: 5px 0; font-size: 0.9em;">${idResult}</div>
                </div>
                <div style="margin-top: 15px; padding: 10px; background: ${passed ? '#e8f5e8' : '#ffebee'}; font-weight: bold;">
                    Overall Result: ${passed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}
                </div>
            `;
            
            document.getElementById('test2-result').innerHTML = results;
        }

        // Test 3: Interactive test
        function testTimezoneConversion() {
            const datetime = document.getElementById('test-datetime').value;
            const timezone = document.getElementById('test-timezone').value;
            
            if (!datetime) {
                document.getElementById('test3-result').innerHTML = '<div style="color: red;">Please select a date and time</div>';
                return;
            }
            
            const enResult = updateHelperText(datetime, timezone, 'en');
            const idResult = updateHelperText(datetime, timezone, 'id');
            
            const results = `
                <h4>English Version:</h4>
                <div style="margin: 5px 0; padding: 10px; background: #f0f8ff; border-radius: 3px;">${enResult}</div>
                <h4>Indonesian Version:</h4>
                <div style="margin: 5px 0; padding: 10px; background: #f0f8ff; border-radius: 3px;">${idResult}</div>
            `;
            
            document.getElementById('test3-result').innerHTML = results;
        }

        // Run tests on page load
        window.onload = function() {
            runTest1();
            runTest2();
        };
    </script>
</body>
</html>
